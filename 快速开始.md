# 同花顺窗口截图和模板匹配功能 - 快速开始

## 🚀 快速开始

### 1. 安装依赖包

**方法一：自动安装（推荐）**
```bash
python install_dependencies.py
```

**方法二：手动安装**
```bash
pip install opencv-python numpy Pillow pywin32
```

**方法三：从requirements.txt安装**
```bash
pip install -r requirements.txt
```

### 2. 运行测试

```bash
python test_screenshot_features.py
```

选择"5. 运行所有测试"来验证功能是否正常。

### 3. 体验功能

```bash
python screenshot_template_demo.py
```

## 📸 主要功能

### 窗口截图
```python
from open_ths_pyauto import *

# 启动同花顺并连接窗口
process = launch_tonghuashun_process()
main_window = connect_to_window(process)

# 截图并保存
screenshot = capture_window_screenshot(main_window, "screenshot.png")
```

### 模板匹配
```python
# 在截图中查找模板
result = find_template_in_screenshot(screenshot, "button_template.png")

if result['found']:
    print(f"找到模板! 置信度: {result['confidence']:.3f}")
    print(f"位置: {result['center']}")
```

### 自动点击
```python
# 查找模板并自动点击
success = click_template_if_found(main_window, "search_box_template.png")

if success:
    # 点击成功后输入内容
    pywinauto.keyboard.send_keys("000001{ENTER}")
```

## 🎯 使用场景

1. **自动搜索股票**
   - 截图保存搜索框模板
   - 自动点击搜索框并输入股票代码

2. **自动交易操作**
   - 保存买入/卖出按钮模板
   - 自动点击交易按钮

3. **界面状态检测**
   - 检测特定界面元素是否出现
   - 根据界面状态执行不同操作

4. **批量操作**
   - 循环查找和点击多个目标
   - 自动化重复性操作

## 📝 创建模板的步骤

1. **截图**: 运行程序对同花顺窗口截图
2. **裁剪**: 用图像编辑软件裁剪出目标区域
3. **保存**: 保存为PNG格式，使用描述性文件名
4. **测试**: 运行模板匹配测试匹配效果

## ⚙️ 参数调优

### 匹配阈值
- **0.9-1.0**: 非常严格，要求几乎完全匹配
- **0.8-0.9**: 严格匹配（推荐默认值）
- **0.7-0.8**: 中等匹配，允许一些差异
- **0.6-0.7**: 宽松匹配，可能有误匹配

### 模板质量
- **大小**: 50x50 到 200x200 像素
- **内容**: 选择独特、稳定的界面元素
- **格式**: PNG格式，保持清晰度

## 🔧 故障排除

### 常见问题

**Q: 模板匹配失败**
- 检查模板文件是否存在
- 降低匹配阈值（如从0.8改为0.7）
- 重新制作更清晰的模板

**Q: 点击位置不准确**
- 使用click_offset参数调整点击位置
- 确保窗口完全可见且在前台

**Q: 截图失败**
- 确保同花顺窗口在前台
- 检查依赖包是否正确安装
- 重新连接窗口

### 调试技巧

1. **保存截图**: 总是保存截图文件以便检查
2. **打印结果**: 输出匹配结果的详细信息
3. **降低阈值**: 从高阈值开始逐步降低
4. **可视化**: 使用图像编辑软件查看模板和截图

## 📁 文件说明

- `open_ths_pyauto.py` - 主程序文件（包含新功能）
- `screenshot_template_demo.py` - 功能演示程序
- `test_screenshot_features.py` - 测试程序
- `install_dependencies.py` - 依赖安装程序
- `requirements.txt` - 依赖包列表
- `README_screenshot_template.md` - 详细文档

## 🎉 完整示例

```python
from open_ths_pyauto import *
import time

def auto_search_stock(stock_code):
    """自动搜索股票的完整示例"""
    
    # 1. 启动同花顺
    process = launch_tonghuashun_process()
    if not process:
        return False
    
    # 2. 连接窗口
    main_window = connect_to_window(process)
    if not main_window:
        return False
    
    # 3. 截图保存当前状态
    capture_window_screenshot(main_window, "before_search.png")
    
    # 4. 查找并点击搜索框
    if click_template_if_found(main_window, "search_box_template.png"):
        # 5. 输入股票代码
        time.sleep(0.5)
        pywinauto.keyboard.send_keys(f"{stock_code}{{ENTER}}")
        
        # 6. 等待搜索结果
        time.sleep(2)
        
        # 7. 截图保存搜索结果
        capture_window_screenshot(main_window, "after_search.png")
        
        print(f"成功搜索股票: {stock_code}")
        return True
    else:
        print("未找到搜索框")
        return False

# 使用示例
if __name__ == "__main__":
    auto_search_stock("000001")  # 搜索平安银行
```

现在你可以开始使用这些强大的截图和模板匹配功能了！🎊
