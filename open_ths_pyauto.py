import subprocess
import os
import time
from pywinauto import Application
import pywinauto
try:
    import cv2
    import numpy as np
    from PIL import ImageGrab
    import win32gui
    import win32con
    SCREENSHOT_AVAILABLE = True
except ImportError as e:
    print(f"警告: 截图功能依赖包未安装: {e}")
    print("请运行: pip install opencv-python numpy Pillow pywin32")
    SCREENSHOT_AVAILABLE = False

def launch_tonghuashun_process(ths_path=r"C:\同花顺软件\同花顺金融大师\hexin.exe"):
    """
    启动同花顺软件进程

    Args:
        ths_path (str): 同花顺软件路径

    Returns:
        subprocess.Popen: 进程对象，如果失败返回None
    """
    # 检查软件是否存在
    if not os.path.exists(ths_path):
        print(f"错误：找不到同花顺软件，路径：{ths_path}")
        return None

    try:
        print("正在启动同花顺软件...")
        process = subprocess.Popen(ths_path)
        print(f"同花顺进程已启动，PID: {process.pid}")
        return process
    except Exception as e:
        print(f"启动同花顺软件失败：{str(e)}")
        return None

def connect_to_window(process, max_retries=15, wait_time=2):
    """
    连接到同花顺窗口

    Args:
        process: 进程对象
        max_retries (int): 最大重试次数
        wait_time (int): 每次重试间隔时间

    Returns:
        pywinauto窗口对象，如果失败返回None
    """
    print("等待同花顺软件完全启动...")
    time.sleep(8)  # 等待软件启动

    app = None
    main_window = None
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 方法1：通过进程ID连接
            app = Application().connect(process=process.pid)
            windows = app.windows()

            # 查找主窗口（通常是可见的最大窗口）
            for window in windows:
                if window.is_visible() and window.rectangle().width() > 100:
                    main_window = window
                    print(f"通过进程ID找到窗口：{window.window_text()}")
                    break

            if main_window:
                break

        except Exception as e:
            print(f"进程ID连接尝试 {retry_count + 1}: {str(e)}")

        # 方法2：通过窗口标题连接
        try:
            possible_titles = ["同花顺", "hexin", "通达信", "金融大师", "THS"]
            for title in possible_titles:
                try:
                    app = Application().connect(title_re=f".*{title}.*")
                    windows = app.windows()
                    if windows:
                        for window in windows:
                            if window.is_visible():
                                main_window = window
                                print(f"通过标题找到窗口：{window.window_text()}")
                                break
                        if main_window:
                            break
                except:
                    continue

            if main_window:
                break

        except Exception as e:
            print(f"标题连接尝试 {retry_count + 1}: {str(e)}")

        time.sleep(wait_time)
        retry_count += 1
        print(f"等待窗口加载... ({retry_count}/{max_retries})")

    if main_window is None:
        print("错误：无法获取同花顺主窗口")
        print("请确保同花顺软件已正常启动")
        return None

    print(f"成功获取窗口句柄：{main_window.handle}")
    return main_window

def click_search_box(main_window, search_text="", press_enter=True):
    """
    点击搜索框并输入内容，可选择是否敲回车键确认

    Args:
        main_window: pywinauto窗口对象
        search_text (str): 要输入的搜索内容
        press_enter (bool): 是否在输入完成后敲回车键，默认为True

    Returns:
        bool: 操作是否成功
    """
    try:
        print("正在查找搜索框...")
        
        # 方法1：通过Value属性定位
        try:
            search_box = main_window.child_window(
                control_type="Edit",
                title="代码/名称/简拼/功能"
            )
            print("通过Value属性找到搜索框")
            search_box.click()
            
            if search_text:
                time.sleep(0.5)  # 等待焦点
                # 清除现有内容并输入新内容
                search_box.select_all()
                search_box.type_keys(search_text)
                print(f"已输入搜索内容：{search_text}")
                
                # 敲回车键确认
                if press_enter:
                    time.sleep(0.3)  # 短暂等待输入完成
                    search_box.type_keys("{ENTER}")
                    print("已敲回车键确认")
            
            return True
            
        except Exception as e1:
            print(f"方法1失败：{e1}")
            
        # 方法2：遍历查找编辑框
        try:
            print("尝试遍历查找编辑框...")
            edit_controls = main_window.descendants(control_type="Edit")
            
            for i, ctrl in enumerate(edit_controls):
                try:
                    # 检查控件文本
                    ctrl_text = ctrl.window_text()
                    print(f"找到编辑框 {i+1}: '{ctrl_text}'")
                    
                    if "代码/名称/简拼/功能" in ctrl_text or ctrl_text == "":
                        print(f"选择编辑框 {i+1} 进行点击")
                        ctrl.click()
                        
                        if search_text:
                            time.sleep(0.5)
                            ctrl.select_all()
                            ctrl.type_keys(search_text)
                            print(f"已输入搜索内容：{search_text}")
                            
                            # 敲回车键确认
                            if press_enter:
                                time.sleep(0.3)
                                ctrl.type_keys("{ENTER}")
                                print("已敲回车键确认")
                        
                        return True
                        
                except Exception as e2:
                    print(f"处理编辑框 {i+1} 时出错：{e2}")
                    continue
                    
        except Exception as e3:
            print(f"方法2失败：{e3}")
            
        # 方法3：使用坐标点击（备用方案）
        print("尝试使用坐标点击...")
        x = (1617 + 1807) // 2  # 1712
        y = (1058 + 1074) // 2  # 1066
        
        pywinauto.mouse.click(coords=(x, y))
        print(f"已点击坐标 ({x}, {y})")
        
        if search_text:
            time.sleep(0.5)
            # 使用键盘输入
            pywinauto.keyboard.send_keys("^a")  # Ctrl+A 全选
            time.sleep(0.2)
            pywinauto.keyboard.send_keys(search_text)
            print(f"已输入搜索内容：{search_text}")
            
            # 敲回车键确认
            if press_enter:
                time.sleep(0.3)
                pywinauto.keyboard.send_keys("{ENTER}")
                print("已敲回车键确认")
        
        return True
        
    except Exception as e:
        print(f"点击搜索框失败：{str(e)}")
        return False

def search_stock(main_window, stock_code, wait_after_enter=2):
    """
    搜索股票的便捷函数
    
    Args:
        main_window: pywinauto窗口对象
        stock_code (str): 股票代码或名称
        wait_after_enter (int): 敲回车后等待时间（秒）
    
    Returns:
        bool: 操作是否成功
    """
    print(f"正在搜索股票：{stock_code}")
    
    success = click_search_box(main_window, stock_code, press_enter=True)
    
    if success and wait_after_enter > 0:
        print(f"等待 {wait_after_enter} 秒加载搜索结果...")
        time.sleep(wait_after_enter)
    
    return success

def check_screenshot_dependencies():
    """
    检查截图功能的依赖是否可用

    Returns:
        bool: 依赖是否可用
    """
    if not SCREENSHOT_AVAILABLE:
        print("错误: 截图功能不可用，请安装依赖包:")
        print("pip install opencv-python numpy Pillow pywin32")
        return False
    return True

def capture_window_screenshot(window, save_path=None):
    """
    对指定窗口进行截图

    Args:
        window: pywinauto窗口对象
        save_path (str): 保存截图的路径，如果为None则不保存

    Returns:
        numpy.ndarray: 截图的numpy数组，如果失败返回None
    """
    if not check_screenshot_dependencies():
        return None

    try:
        # 获取窗口句柄
        hwnd = window.handle

        # 将窗口置于前台
        win32gui.SetForegroundWindow(hwnd)
        time.sleep(0.5)  # 等待窗口切换

        # 获取窗口位置和大小
        rect = win32gui.GetWindowRect(hwnd)
        left, top, right, bottom = rect
        width = right - left
        height = bottom - top

        print(f"窗口位置: ({left}, {top}), 大小: {width}x{height}")

        # 截图
        screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))

        # 转换为numpy数组
        screenshot_np = np.array(screenshot)

        # 转换颜色格式 (PIL使用RGB，OpenCV使用BGR)
        screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

        # 保存截图
        if save_path:
            cv2.imwrite(save_path, screenshot_bgr)
            print(f"截图已保存到: {save_path}")

        return screenshot_bgr

    except Exception as e:
        print(f"截图失败: {str(e)}")
        return None

def find_template_in_screenshot(screenshot, template_path, threshold=0.8):
    """
    在截图中查找模板图像

    Args:
        screenshot: 截图的numpy数组
        template_path (str): 模板图像文件路径
        threshold (float): 匹配阈值，默认0.8

    Returns:
        dict: 包含匹配结果的字典，格式为:
              {
                  'found': bool,  # 是否找到
                  'confidence': float,  # 最高匹配度
                  'location': tuple,  # 匹配位置 (x, y)
                  'center': tuple,  # 匹配中心点 (x, y)
                  'rectangle': tuple  # 匹配矩形 (x, y, w, h)
              }
    """
    if not check_screenshot_dependencies():
        return {'found': False, 'confidence': 0, 'location': None, 'center': None, 'rectangle': None}

    try:
        # 检查模板文件是否存在
        if not os.path.exists(template_path):
            print(f"模板文件不存在: {template_path}")
            return {'found': False, 'confidence': 0, 'location': None, 'center': None, 'rectangle': None}

        # 读取模板图像
        template = cv2.imread(template_path, cv2.IMREAD_COLOR)
        if template is None:
            print(f"无法读取模板图像: {template_path}")
            return {'found': False, 'confidence': 0, 'location': None, 'center': None, 'rectangle': None}

        # 获取模板尺寸
        template_height, template_width = template.shape[:2]

        # 模板匹配
        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)

        # 获取最佳匹配位置
        _, max_val, _, max_loc = cv2.minMaxLoc(result)

        # 判断是否找到匹配
        found = max_val >= threshold

        if found:
            # 计算匹配位置和中心点
            top_left = max_loc
            center_x = top_left[0] + template_width // 2
            center_y = top_left[1] + template_height // 2

            result_dict = {
                'found': True,
                'confidence': max_val,
                'location': top_left,
                'center': (center_x, center_y),
                'rectangle': (top_left[0], top_left[1], template_width, template_height)
            }

            print(f"找到模板匹配! 置信度: {max_val:.3f}, 位置: {top_left}, 中心: ({center_x}, {center_y})")
        else:
            result_dict = {
                'found': False,
                'confidence': max_val,
                'location': None,
                'center': None,
                'rectangle': None
            }
            print(f"未找到模板匹配. 最高置信度: {max_val:.3f} (阈值: {threshold})")

        return result_dict

    except Exception as e:
        print(f"模板匹配失败: {str(e)}")
        return {'found': False, 'confidence': 0, 'location': None, 'center': None, 'rectangle': None}

def click_template_if_found(window, template_path, threshold=0.8, click_offset=(0, 0)):
    """
    在窗口中查找模板并点击

    Args:
        window: pywinauto窗口对象
        template_path (str): 模板图像文件路径
        threshold (float): 匹配阈值
        click_offset (tuple): 点击偏移量 (x_offset, y_offset)

    Returns:
        bool: 是否成功找到并点击
    """
    try:
        # 截图
        screenshot = capture_window_screenshot(window)
        if screenshot is None:
            return False

        # 查找模板
        match_result = find_template_in_screenshot(screenshot, template_path, threshold)

        if match_result['found']:
            # 获取窗口位置
            rect = win32gui.GetWindowRect(window.handle)
            window_left, window_top = rect[0], rect[1]

            # 计算点击坐标（相对于屏幕）
            center_x, center_y = match_result['center']
            click_x = window_left + center_x + click_offset[0]
            click_y = window_top + center_y + click_offset[1]

            # 点击
            pywinauto.mouse.click(coords=(click_x, click_y))
            print(f"已点击模板位置: ({click_x}, {click_y})")

            return True
        else:
            print("未找到模板，无法点击")
            return False

    except Exception as e:
        print(f"模板点击失败: {str(e)}")
        return False

def main():
    """
    主函数：启动同花顺并演示截图和模板匹配功能
    """
    # 启动进程
    process = launch_tonghuashun_process()
    if not process:
        return

    # 连接窗口
    main_window = connect_to_window(process)
    if not main_window:
        return

    # 示例1：对窗口进行截图
    print("\n=== 窗口截图功能演示 ===")
    screenshot_path = "tonghuashun_screenshot.png"
    screenshot = capture_window_screenshot(main_window, screenshot_path)

    if screenshot is not None:
        print(f"截图成功！图像尺寸: {screenshot.shape}")

        # 示例2：模板匹配演示
        print("\n=== 模板匹配功能演示 ===")
        # 注意：你需要准备一个模板图像文件
        template_path = "search_box_template.png"  # 搜索框的模板图像

        if os.path.exists(template_path):
            match_result = find_template_in_screenshot(screenshot, template_path, threshold=0.7)

            if match_result['found']:
                print(f"模板匹配成功！置信度: {match_result['confidence']:.3f}")
                print(f"匹配位置: {match_result['location']}")
                print(f"中心点: {match_result['center']}")

                # 示例3：点击找到的模板位置
                print("\n=== 模板点击功能演示 ===")
                click_success = click_template_if_found(main_window, template_path, threshold=0.7)
                if click_success:
                    print("模板点击成功！")
                    time.sleep(1)

                    # 在点击后输入内容
                    pywinauto.keyboard.send_keys("000001{ENTER}")
                    print("已输入股票代码并回车")
            else:
                print("未找到模板匹配")
        else:
            print(f"模板文件不存在: {template_path}")
            print("请准备一个搜索框的模板图像文件")

    # 原有的搜索功能演示
    print("\n=== 原有搜索功能演示 ===")
    success = search_stock(main_window, "000001")  # 搜索平安银行

    if success:
        print("搜索操作完成")

        # 等待一段时间后进行下一次搜索
        time.sleep(3)

        # 再次截图查看搜索结果
        result_screenshot_path = "search_result_screenshot.png"
        result_screenshot = capture_window_screenshot(main_window, result_screenshot_path)
        if result_screenshot is not None:
            print(f"搜索结果截图已保存: {result_screenshot_path}")

        # 示例：搜索股票名称
        search_stock(main_window, "招商银行")

    else:
        print("搜索操作失败")

    # 可选：打印窗口控件信息用于调试
    # print("\n=== 窗口控件信息 ===")
    # main_window.print_control_identifiers()

def demo_template_creation():
    """
    演示如何创建模板图像的辅助函数
    """
    print("=== 模板创建演示 ===")
    print("1. 首先运行程序对窗口截图")
    print("2. 使用图像编辑软件（如画图、Photoshop等）打开截图")
    print("3. 裁剪出你想要匹配的区域（如搜索框）")
    print("4. 保存为模板图像文件（如 search_box_template.png）")
    print("5. 将模板文件放在程序同目录下")
    print("6. 重新运行程序即可使用模板匹配功能")

def advanced_template_matching_demo(main_window):
    """
    高级模板匹配演示
    """
    print("\n=== 高级模板匹配演示 ===")

    # 多个模板匹配
    templates = [
        ("search_box_template.png", "搜索框"),
        ("buy_button_template.png", "买入按钮"),
        ("sell_button_template.png", "卖出按钮"),
        ("menu_template.png", "菜单按钮")
    ]

    # 截图
    screenshot = capture_window_screenshot(main_window)
    if screenshot is None:
        return

    found_templates = []

    for template_path, template_name in templates:
        if os.path.exists(template_path):
            match_result = find_template_in_screenshot(screenshot, template_path, threshold=0.7)
            if match_result['found']:
                found_templates.append((template_name, match_result))
                print(f"找到 {template_name}: 置信度 {match_result['confidence']:.3f}")
        else:
            print(f"模板文件不存在: {template_path}")

    if found_templates:
        print(f"\n总共找到 {len(found_templates)} 个模板匹配")

        # 可以根据需要点击特定的模板
        for template_name, match_result in found_templates:
            print(f"{template_name} 位置: {match_result['center']}")
    else:
        print("未找到任何模板匹配")

if __name__ == "__main__":
    main()