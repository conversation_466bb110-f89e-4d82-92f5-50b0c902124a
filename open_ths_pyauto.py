import subprocess
import os
import time
from pywinauto import Application
import pywinauto

def launch_tonghuashun_process(ths_path=r"C:\同花顺软件\同花顺金融大师\hexin.exe"):
    """
    启动同花顺软件进程

    Args:
        ths_path (str): 同花顺软件路径

    Returns:
        subprocess.Popen: 进程对象，如果失败返回None
    """
    # 检查软件是否存在
    if not os.path.exists(ths_path):
        print(f"错误：找不到同花顺软件，路径：{ths_path}")
        return None

    try:
        print("正在启动同花顺软件...")
        process = subprocess.Popen(ths_path)
        print(f"同花顺进程已启动，PID: {process.pid}")
        return process
    except Exception as e:
        print(f"启动同花顺软件失败：{str(e)}")
        return None

def connect_to_window(process, max_retries=15, wait_time=2):
    """
    连接到同花顺窗口

    Args:
        process: 进程对象
        max_retries (int): 最大重试次数
        wait_time (int): 每次重试间隔时间

    Returns:
        pywinauto窗口对象，如果失败返回None
    """
    print("等待同花顺软件完全启动...")
    time.sleep(8)  # 等待软件启动

    app = None
    main_window = None
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 方法1：通过进程ID连接
            app = Application().connect(process=process.pid)
            windows = app.windows()

            # 查找主窗口（通常是可见的最大窗口）
            for window in windows:
                if window.is_visible() and window.rectangle().width() > 100:
                    main_window = window
                    print(f"通过进程ID找到窗口：{window.window_text()}")
                    break

            if main_window:
                break

        except Exception as e:
            print(f"进程ID连接尝试 {retry_count + 1}: {str(e)}")

        # 方法2：通过窗口标题连接
        try:
            possible_titles = ["同花顺", "hexin", "通达信", "金融大师", "THS"]
            for title in possible_titles:
                try:
                    app = Application().connect(title_re=f".*{title}.*")
                    windows = app.windows()
                    if windows:
                        for window in windows:
                            if window.is_visible():
                                main_window = window
                                print(f"通过标题找到窗口：{window.window_text()}")
                                break
                        if main_window:
                            break
                except:
                    continue

            if main_window:
                break

        except Exception as e:
            print(f"标题连接尝试 {retry_count + 1}: {str(e)}")

        time.sleep(wait_time)
        retry_count += 1
        print(f"等待窗口加载... ({retry_count}/{max_retries})")

    if main_window is None:
        print("错误：无法获取同花顺主窗口")
        print("请确保同花顺软件已正常启动")
        return None

    print(f"成功获取窗口句柄：{main_window.handle}")
    return main_window

def click_search_box(main_window, search_text="", press_enter=True):
    """
    点击搜索框并输入内容，可选择是否敲回车键确认

    Args:
        main_window: pywinauto窗口对象
        search_text (str): 要输入的搜索内容
        press_enter (bool): 是否在输入完成后敲回车键，默认为True

    Returns:
        bool: 操作是否成功
    """
    try:
        print("正在查找搜索框...")
        
        # 方法1：通过Value属性定位
        try:
            search_box = main_window.child_window(
                control_type="Edit",
                title="代码/名称/简拼/功能"
            )
            print("通过Value属性找到搜索框")
            search_box.click()
            
            if search_text:
                time.sleep(0.5)  # 等待焦点
                # 清除现有内容并输入新内容
                search_box.select_all()
                search_box.type_keys(search_text)
                print(f"已输入搜索内容：{search_text}")
                
                # 敲回车键确认
                if press_enter:
                    time.sleep(0.3)  # 短暂等待输入完成
                    search_box.type_keys("{ENTER}")
                    print("已敲回车键确认")
            
            return True
            
        except Exception as e1:
            print(f"方法1失败：{e1}")
            
        # 方法2：遍历查找编辑框
        try:
            print("尝试遍历查找编辑框...")
            edit_controls = main_window.descendants(control_type="Edit")
            
            for i, ctrl in enumerate(edit_controls):
                try:
                    # 检查控件文本
                    ctrl_text = ctrl.window_text()
                    print(f"找到编辑框 {i+1}: '{ctrl_text}'")
                    
                    if "代码/名称/简拼/功能" in ctrl_text or ctrl_text == "":
                        print(f"选择编辑框 {i+1} 进行点击")
                        ctrl.click()
                        
                        if search_text:
                            time.sleep(0.5)
                            ctrl.select_all()
                            ctrl.type_keys(search_text)
                            print(f"已输入搜索内容：{search_text}")
                            
                            # 敲回车键确认
                            if press_enter:
                                time.sleep(0.3)
                                ctrl.type_keys("{ENTER}")
                                print("已敲回车键确认")
                        
                        return True
                        
                except Exception as e2:
                    print(f"处理编辑框 {i+1} 时出错：{e2}")
                    continue
                    
        except Exception as e3:
            print(f"方法2失败：{e3}")
            
        # 方法3：使用坐标点击（备用方案）
        print("尝试使用坐标点击...")
        x = (1617 + 1807) // 2  # 1712
        y = (1058 + 1074) // 2  # 1066
        
        pywinauto.mouse.click(coords=(x, y))
        print(f"已点击坐标 ({x}, {y})")
        
        if search_text:
            time.sleep(0.5)
            # 使用键盘输入
            pywinauto.keyboard.send_keys("^a")  # Ctrl+A 全选
            time.sleep(0.2)
            pywinauto.keyboard.send_keys(search_text)
            print(f"已输入搜索内容：{search_text}")
            
            # 敲回车键确认
            if press_enter:
                time.sleep(0.3)
                pywinauto.keyboard.send_keys("{ENTER}")
                print("已敲回车键确认")
        
        return True
        
    except Exception as e:
        print(f"点击搜索框失败：{str(e)}")
        return False

def search_stock(main_window, stock_code, wait_after_enter=2):
    """
    搜索股票的便捷函数
    
    Args:
        main_window: pywinauto窗口对象
        stock_code (str): 股票代码或名称
        wait_after_enter (int): 敲回车后等待时间（秒）
    
    Returns:
        bool: 操作是否成功
    """
    print(f"正在搜索股票：{stock_code}")
    
    success = click_search_box(main_window, stock_code, press_enter=True)
    
    if success and wait_after_enter > 0:
        print(f"等待 {wait_after_enter} 秒加载搜索结果...")
        time.sleep(wait_after_enter)
    
    return success

def main():
    """
    主函数：启动同花顺并操作搜索框
    """
    # 启动进程
    process = launch_tonghuashun_process()
    if not process:
        return
    
    # 连接窗口
    main_window = connect_to_window(process)
    if not main_window:
        return
    
    # 示例1：搜索股票代码并敲回车
    success = search_stock(main_window, "000001")  # 搜索平安银行
    
    if success:
        print("搜索操作完成")
        
        # 等待一段时间后进行下一次搜索
        time.sleep(3)
        
        # 示例2：搜索股票名称
        search_stock(main_window, "招商银行")
        
        # 示例3：只输入不敲回车
        # click_search_box(main_window, "中国平安", press_enter=False)
        
    else:
        print("搜索操作失败")
    
    # 可选：打印窗口控件信息用于调试
    # print("\n=== 窗口控件信息 ===")
    # main_window.print_control_identifiers()

if __name__ == "__main__":
    main()