# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块
try:
    import xbot
    from xbot import print, sleep
    from .import package
    from .package import variables as glv
    from .import process1  # prepare_ths_windows
    from .import process9  # 获取THS截图
    from .import get_stock_pool
    from . import capture_imge 
    from . import gen_signal
    # from . import sql_opeators
except:
    import get_stock_pool
    import capture_imge 
    import gen_signal
    # import sql_opeators
    print("running on your personal PC")
import pymysql
from pymysql.cursors import DictCursor
import numpy as np
import os
"""
这个是整个项目的main函数，
主要动作都是由代码完成，
必要步骤用到调用可视版本的子流程来完成，
能用代码完成的尽量用代码完成
"""
import re

def extract_hwnd_regex(text: str) -> int | None:
    """
    使用正则表达式从字符串中提取 hWnd 的值。

    Args:
        text: 包含 hWnd 的原始字符串，例如 "Win32Window(hWnd=132116)"。

    Returns:
        如果找到，返回 hWnd 的整数值 (例如 132116)；否则返回 None。
    """
    # r'hWnd\s*=\s*(\d+)' 是一个正则表达式模式
    # \s* 匹配零个或多个空格，使其能够处理 "hWnd=123" 或 "hWnd = 123" 等情况
    # (\d+) 匹配一个或多个数字，并将其作为一个“捕获组”
    pattern = r'hWnd\s*=\s*(\d+)'
    
    # re.search 会在字符串中查找第一个匹配项
    match = re.search(pattern, text)
    
    # 如果找到了匹配项
    if match:
        # match.group(1) 获取第一个捕获组的内容（也就是括号里的 \d+ 匹配到的数字）
        hwnd_str = match.group(1)
        return int(hwnd_str)
    
    # 如果没有找到匹配项
    return None
def extract_stock_code(code):
    """提取股票代码中的数字部分"""
    match = re.search(r'\d+', code)
    return match.group() if match else code

def main(args):
    # step1 prepare ths window
    # 准备激活的窗口，然后进行返回
    # 窗口的实例为ths_windows = args['ths_window']
    args= {}
    # process1.main(args)
    ths_window = args['ths_window']
    hwnd_value = extract_hwnd_regex(str(ths_window))
    # step2 prepare favorite stock list
    db_connection_config = {
        'host': '**************',
        'user': 'ken',
        'password': 'li173312',
        'database': 'ths_stock',
        'charset': 'utf8mb4', # 建议总是指定字符集
        'cursorclass': pymysql.cursors.Cursor # 使用标准游标
    }
    stock_list = get_stock_pool.fetch_stock_data(db_connection_config,table_name='favorite_list')

    flag = 0
    while True:
        # step3 对股票进行循环
        for index, stock_t in enumerate(stock_list):
            # 测试
            stock= []
            stock.append(extract_stock_code(stock_t[0]))
            stock.append(stock_t[1])
            for flag_index in range(2):
                # step 4 对单独的这只股票执行可视化操作
                # 传入激活的窗口、股票的名字， 分钟数、和config文件
                args['stock_info'] = stock
                args['period'] = 35 # 35代表60mins
                process9.main(args)

                # step 5 对窗口进行截图
                image_path = capture_imge.get_img_save_path(args['stock_info'][0], args['period'])
                xbot.win32.screenshot.save_window_to_file(hwnd_value, image_path[2], 'png')
                # todo 后面这里改成直接截图成变量进行传递

                # Step 6 对截图的信号进行判断，同时存储的sql
                current_file_path = os.path.abspath(__file__)
                current_dir_path = os.path.dirname(current_file_path)
                config_path = os.path.join(current_dir_path, 'resources\\coor_config.json')
                
                # 构造股票的字典
                stock_value_dict = {}
                stock_value_dict['code'] = stock[0]
                stock_value_dict['name'] = stock[1]
                stock_value_dict['img_save_path'] = image_path[2]
                stock_value_dict['LD_flag_list'] = np.zeros(14)
                stock_value_dict['LM_flag_list'] = np.zeros(14)
                stock_value_dict['field'] = [
                'LD_flag_14', 'LD_flag_13', 'LD_flag_12', 'LD_flag_11', 'LD_flag_10', 'LD_flag_9', 'LD_flag_8', 'LD_flag_7', 
                'LD_flag_6', 'LD_flag_5', 'LD_flag_4', 'LD_flag_3', 'LD_flag_2', 'LD_flag_1',  
                'LM_flag_14', 'LM_flag_13', 'LM_flag_12', 'LM_flag_11', 'LM_flag_10', 'LM_flag_9', 'LM_flag_8', 'LM_flag_7', 
                'LM_flag_6', 'LM_flag_5', 'LM_flag_4', 'LM_flag_3', 'LM_flag_2', 'LM_flag_1']
                
                # stock_value_dict = gen_signal.keep_last_status_sql(stock_value_dict)
                signal = gen_signal.deal_signal_60mins(stock_value_dict, config_path)
                if signal[0] != 0:
                    flag_index = 1
                    break
                
                
                
if __name__ == '__main__':
    main([])
