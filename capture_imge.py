# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

try:
    import xbot
    from xbot import print, sleep
    from .import package
    from .package import variables as glv
except:
    print("running in your system")

import pyautogui
from PIL import Image, ImageTk, ImageDraw
import cv2
import numpy as np
from datetime import datetime
import os
import threading
import time
from typing import Tuple, Optional, Callable
import pygetwindow as gw
from datetime import datetime

class ScreenshotTool:
    def __init__(self):
        """初始化截图工具"""
        # 禁用pyautogui的安全检查
        pyautogui.FAILSAFE = False
        
        # 创建保存目录
        self.save_directory = "screenshots"
        if not os.path.exists(self.save_directory):
            os.makedirs(self.save_directory)
    
    def capture_region(self, x: int, y: int, width: int, height: int, 
                      save_path: Optional[str] = None) -> Image.Image:
        """
        截取指定区域
        
        Args:
            x: 左上角x坐标
            y: 左上角y坐标
            width: 宽度
            height: 高度
            save_path: 保存路径（可选）
            
        Returns:
            PIL Image对象
        """
        try:
            # 使用pyautogui截图
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            
            # 保存截图
            if save_path:
                screenshot.save(save_path)
                print(f"截图已保存到: {save_path}")
            
            return screenshot
            
        except Exception as e:
            print(f"截图失败: {e}")
            return None
    
    def capture_full_screen(self, save_path: Optional[str] = None) -> Image.Image:
        """
        全屏截图
        
        Args:
            save_path: 保存路径（可选）
            
        Returns:
            PIL Image对象
        """
        try:
            screenshot = pyautogui.screenshot()
            
            if save_path:
                screenshot.save(save_path)
                print(f"全屏截图已保存到: {save_path}")
            
            return screenshot
            
        except Exception as e:
            print(f"全屏截图失败: {e}")
            return None
    
    def capture_window(self, window_title: str, save_path: Optional[str] = None) -> Image.Image:
        """
        截取指定窗口
        
        Args:
            window_title: 窗口标题（支持模糊匹配）
            save_path: 保存路径（可选）
            
        Returns:
            PIL Image对象
        """
        try:
            # 查找窗口
            windows = gw.getWindowsWithTitle(window_title)
            if not windows:
                # 尝试模糊匹配
                all_windows = gw.getAllWindows()
                windows = [w for w in all_windows if window_title.lower() in w.title.lower()]
            
            if not windows:
                print(f"未找到窗口: {window_title}")
                return None
            
            window = windows[0]
            
            # 激活窗口
            if window.isMinimized:
                window.restore()
            window.activate()
            time.sleep(0.1)
            
            # 截取窗口区域
            screenshot = pyautogui.screenshot(region=(
                window.left, window.top, window.width, window.height
            ))
            
            if save_path:
                screenshot.save(save_path)
                print(f"窗口截图已保存到: {save_path}")
            
            return screenshot
            
        except Exception as e:
            print(f"窗口截图失败: {e}")
            return None
    
    def generate_filename(self, prefix: str = "screenshot", extension: str = "png") -> str:
        """
        生成带时间戳的文件名
        
        Args:
            prefix: 文件名前缀
            extension: 文件扩展名
            
        Returns:
            完整的文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{prefix}_{timestamp}.{extension}"
        return os.path.join(self.save_directory, filename)
    
    def delayed_screenshot(self, delay: int, x: int, y: int, width: int, height: int,
                          save_path: Optional[str] = None) -> Image.Image:
        """
        延时截图
        
        Args:
            delay: 延时秒数
            x, y, width, height: 截图区域
            save_path: 保存路径
            
        Returns:
            PIL Image对象
        """
        print(f"将在 {delay} 秒后截图...")
        time.sleep(delay)
        return self.capture_region(x, y, width, height, save_path)
    
    def capture_multiple_regions(self, regions: list, save_directory: Optional[str] = None) -> list:
        """
        批量截取多个区域
        
        Args:
            regions: 区域列表，每个元素为 (x, y, width, height, name)
            save_directory: 保存目录
            
        Returns:
            截图对象列表
        """
        screenshots = []
        save_dir = save_directory or self.save_directory
        
        for i, region in enumerate(regions):
            if len(region) == 5:
                x, y, width, height, name = region
                save_path = os.path.join(save_dir, f"{name}.png")
            else:
                x, y, width, height = region
                save_path = os.path.join(save_dir, f"region_{i+1}.png")
            
            screenshot = self.capture_region(x, y, width, height, save_path)
            screenshots.append(screenshot)
            time.sleep(0.1)  # 短暂延时避免过快截图
        
        return screenshots

# 简单使用函数
def quick_screenshot(x: int, y: int, width: int, height: int, filename: str = None):
    """
    快速截图函数
    
    Args:
        x, y: 左上角坐标
        width, height: 宽度和高度
        filename: 保存文件名（可选）
    """
    tool = ScreenshotTool()
    if filename is None:
        filename = tool.generate_filename()
    tool.capture_region(x, y, width, height, filename)
    return filename


def get_timestamp_str():
    """
    获取当前时间，格式为：YYYYMMDD_HHMMSS
    用于文件命名
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")

# 使用示例
def capture_img(stock_name,var_period):
    # 方法1: 直接使用类
    tool = ScreenshotTool()
    # 截取指定区域 (x=100, y=100, width=800, height=600)
    # save_path = tool.generate_filename("test_region")
    p_path = rf"C:\Users\<USER>\Documents\ths_gs_resource\img\{var_period}"
    filename = f"{stock_name}_screenshot_{get_timestamp_str()}.png"
    save_path = os.path.join(p_path, filename)
    tool.capture_region(741, 120, 563, 421, save_path)
    print(f"截图已保存到: {save_path}")

def capture_days_img(args,var_list):
    # 方法1: 直接使用类
    tool = ScreenshotTool()
    # 截取指定区域 (x=100, y=100, width=800, height=600)
    # save_path = tool.generate_filename("test_region")
    p_path = r"C:\Users\<USER>\Documents\ths_gs_resource\img\60mins"
    filename = f"{args.stock_name}_screenshot_{get_timestamp_str()}.png"
    save_path = os.path.join
    tool.capture_region(739, 162, 890, 515, save_path)
    print(f"截图已保存到: {save_path}")

def get_img_save_path(stock_name,var_period):
    p_path = rf"C:\Users\<USER>\Documents\ths_gs_resource\img\{var_period}"
    filename = f"{stock_name}_screenshot_{get_timestamp_str()}.png"
    save_path = [p_path, filename, os.path.join(p_path, filename)]
    return save_path

def resize_img_fullimg(image_path, point=None):
    if point == None:
        p0 = (136, 162)  # 左上角坐标
        p1 = (1629, 677)  # 右下角坐标
    else:
        p0 = point[0]
        p1 = point[1]
    # 读取图片
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法加载图片: {image_path}")
        exit()
    # 设置裁剪区域 (左上角x, 左上角y, 右下角x, 右下角y)
    x1, y1 = p0[0], p0[1]  # 左上角
    x2, y2 = p1[0], p1[1]  # 右下角
    # 裁剪图片
    cropped = image[y1:y2, x1:x2]
    # # 显示裁剪结果
    # cv2.imshow('Cropped Image', cropped)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()
    dir_path, file_name = os.path.split(image_path)
    cropped_path = os.path.join(dir_path,"cropped_kline_"+file_name)
    # 保存裁剪后的图片
    cv2.imwrite(cropped_path, cropped)
    return cropped_path

def main(args):
    pass


if __name__ == '__main__':
    capture_img()


