{"uuid": "6f4d191f-e465-4c3a-950b-a3955030c4d9", "name": "GS量化策略", "icon": null, "version": "1", "tags": null, "software": null, "software_title": null, "package_version": 7, "feature_list": [2, 5, 7], "description": null, "instruction": "", "use_latest_pip": false, "videoName": "", "startup": "main", "robot_type": "app", "activity_code": null, "flows": [{"name": "main", "filename": "main", "kind": "Visual", "opened": false, "groupName": null}, {"name": "open_ths_window", "filename": "process1", "kind": "Visual", "opened": false, "groupName": null}, {"name": "get_stock_pool", "filename": "get_stock_pool", "kind": "Code", "opened": false, "groupName": null}, {"name": "遍历日线", "filename": "process2", "kind": "Visual", "opened": false, "groupName": null}, {"name": "遍历60min", "filename": "process3", "kind": "Visual", "opened": false, "groupName": null}, {"name": "capture_imge", "filename": "capture_imge", "kind": "Code", "opened": false, "groupName": null}, {"name": "img_detetor", "filename": "img_detetor", "kind": "Code", "opened": false, "groupName": null}, {"name": "gen_signal", "filename": "gen_signal", "kind": "Code", "opened": false, "groupName": null}, {"name": "子流程1", "filename": "process5", "kind": "Visual", "opened": false, "groupName": null}, {"name": "save_data_to_sql", "filename": "process6", "kind": "Visual", "opened": false, "groupName": null}, {"name": "窗口坐标预处理", "filename": "process7", "kind": "Visual", "opened": false, "groupName": null}, {"name": "lines_detetor", "filename": "lines_detetor", "kind": "Code", "opened": false, "groupName": null}, {"name": "task_main", "filename": "task_main", "kind": "Code", "opened": false, "groupName": null}, {"name": "update_sql", "filename": "update_sql", "kind": "Code", "opened": false, "groupName": null}, {"name": "获取THS截图", "filename": "process9", "kind": "Visual", "opened": false, "groupName": null}, {"name": "sql_opeator", "filename": "sql_opeator", "kind": "Code", "opened": false, "groupName": null}, {"name": "pyauto_opeator_windows", "filename": "pyauto_opeator_windows", "kind": "Code", "opened": false, "groupName": null}], "flow_groups": [], "variables": [], "external_dependencies": ["pandas==2.3.1", "PyAutoGUI==0.9.54", "pillow==11.3.0", "matplotlib==3.10.5", "numpy==2.2.6", "opencv-python==*********", "mysql-connector-python==9.4.0", "PyMySQL==1.1.1", "cryptography==45.0.6"], "internaldependencies": ["activity_5a54072e==23.3.0", "activity_sql_server==25.6.0"], "selectordependencies": [], "internalautodependencies": ["activity_5a54072e", "activity_sql_server"], "ipaasDependencies": [], "databook_columns": [], "authority": "use", "internalautoupgrade": false, "isbrief": false, "uia_type": "PC", "persist_databook": false, "customItems": {"gifUrl": null, "videoUrl": "", "imageUrl": "", "imageName": ""}}